import { Hono } from 'hono';
import { agentsMiddleware } from 'hono-agents';
import { <PERSON>kuApp } from '../common/types';
import { DashboardLayoutCard, LayoutWithCard } from '../ui/components/layout';
import { passwordEncoder } from '../common/security/passwordEncoder';
import { capitalize } from '../workflow/utils/helpers';
import { BrowserStateService } from '../workflow/BrowserStateService';
import { R2BrowserStateRepository } from '../workflow/R2BrowserStateRepository';
import { SimulatedLoginHandler } from './simulated-login-handler';
import { CDPBrowserDataAdapter } from '../workflow/adapters/CDPBrowserDataAdapter';
import { BrowserServiceFactory } from '../workflow/services';
import { CDP } from '../browser/simple-cdp';
import { platformDetails } from '../ui/constants';

const app = new Hono<KakuApp>();

// Note: Agent class names are transformed to kebab-case in URLs
// Example: ConnectionAgent → /agents/connection-agent/[platformId]
// In our case, it's /agents/connections/[platformId]
app
  .use(
    '*',
    agentsMiddleware({
      options: {
        cors: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type', // 'Access-Control-Allow-Headers': 'Content-Type', //
        },
        prefix: 'agents',
        // onBeforeRequest: async (request) => {
        //   const response = await validateAuth(request);
        //   if (response instanceof Response) {
        //     return response;
        //   }
        // },
      },
      onError: (error) => {
        console.error('Agent middleware error:', error);
      },
    }),
  )
  .get('connect/:userId/:serviceId', (c) => {
    const userId = c.req.param().userId;
    const serviceId = c.req.param().serviceId;
    return c.html(DashboardLayoutCard(userId, serviceId, capitalize(serviceId)));
  })
  .get('connect/:userId/:serviceId/flow', (c) => {
    const userId = c.req.param().userId;
    const serviceId = c.req.param().serviceId;
    const url = `${c.env.KAKU_WS_ENDPOINT}/agents/connections/${userId}:${serviceId}`;
    return c.html(
      LayoutWithCard(
        { wsEndpoint: url, userId },
        {
          serviceId: serviceId,
          serviceName: capitalize(serviceId),
          serviceLogo: `/fb.png`,
          serviceTitle: 'Testing Forms',
          formTitle: 'Testing Forms',
          serviceDescription: 'Testing Forms',
          liveViewToggleText: 'Show Live View',
          loadingText: 'Processing...',
        },
      ),
    );
  })
  .get('/test-password', async (c) => {
    const matches = await passwordEncoder().matches(
      'this is an awesome password',
      '{argon2@SpringSecurity_v5_8}$argon2id$v=19$m=16384,t=2,p=1$J1lDBqWTpije3hfNicl6nA$Ktm+S5MLIWMeGMJ3v7QWCCW6U83Ub4MUisYPwlG8Zcs',
    );
    return c.html(`Password matches: ${matches}`);
  })
  .get('/handle/:userId/:platformId', async (c) => {
    const userId = c.req.param().userId;
    const platformId = c.req.param().platformId;
    const connectionDOName = `${userId}:${platformId}`;
    const agent = c.env.Connections.idFromName(connectionDOName);
    const stub = c.env.Connections.get(agent);
    await stub.setName(connectionDOName);
    await stub.handleFlowInitiate({ platform: 'test' });
    return c.json({ success: true });
  })
  .post('/demo-session/:userId/:platformId', async (c) => {
    try {
      const userId = c.req.param().userId;
      const platformId = c.req.param().platformId;
      const browserStateService = new BrowserStateService(
        new R2BrowserStateRepository(c.env.SCREENSHOTS_INBOUND_BUCKET),
      );

      const storedSession = await browserStateService.getBrowserState(userId, platformId);

      if (!storedSession) {
        return c.json({
          success: false,
          error: 'No stored session found for this user and platform',
        });
      }

      console.log(`✓ Found stored session with ${storedSession.cookies.length} cookies`);

      const browserService = BrowserServiceFactory.createFromEnvironment(c.env);
      const browserSession = await browserService.createSession({
        browserArgs: [],
      });

      console.log('✓ Created new browser session');

      const cdpClient = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });
      const targetInfo = await cdpClient.Target.createTarget({ url: 'about:blank' });
      console.log('✓ Created new target:', targetInfo.targetId);

      const { sessionId } = await cdpClient.Target.attachToTarget({
        targetId: targetInfo.targetId,
        flatten: true,
      });

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      console.log('✓ CDP session established with sessionId:', sessionId);

      const browserDataAdapter = new CDPBrowserDataAdapter(cdpClient, sessionId);
      await browserStateService.loadBrowserStateToPage(browserDataAdapter, userId, platformId);

      const targetUrl =
        platformDetails[platformId as keyof typeof platformDetails]?.loginLink ||
        'https://example.com';

      await cdpClient.Page.navigate({ url: targetUrl }, sessionId);

      await new Promise((resolve) => {
        const handler = ({ params }: { params: any }) => {
          if (params.name === 'networkIdle') {
            cdpClient.Page.removeEventListener('loadEventFired', handler);
            resolve(undefined);
          }
        };
        cdpClient.Page.addEventListener('loadEventFired', handler);

        setTimeout(resolve, 10000);
      });

      console.log(`✓ Navigated to ${targetUrl} with restored session`);

      await browserService.closeSession(browserSession.sessionId!);
      return c.json({
        success: true,
        message: 'Session demonstration completed successfully',
        details: {
          cookiesLoaded: storedSession.cookies.length,
          localStorageItems: Object.keys(storedSession.localStorageData).length,
          sessionStorageItems: Object.keys(storedSession.sessionStorageData).length,
          targetUrl: targetUrl,
        },
      });
    } catch (error) {
      console.error('Session demonstration failed:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  });

app.route('/v1/simulated', SimulatedLoginHandler);

export default app;

export { ConnectionsWorkflow } from '../workflow/connections-workflow';
export { Connections } from '../agent/connection-agent';
