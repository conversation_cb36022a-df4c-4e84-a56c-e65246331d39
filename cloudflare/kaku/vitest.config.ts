import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';

export default defineWorkersConfig(async () => {
  return {
    test: {
      setupFiles: ['./vitest.setup.ts'],
    
      testTimeout: 30000,
      globals: true,
      poolOptions: {
        workers: {
          singleWorker: false,
          isolatedStorage: false,
          miniflare: {
            compatibilityDate: '2025-03-17',
            compatibilityFlags: ['nodejs_compat'],
            workflows: {
              'connections-workflow': {
                binding: 'CONNECTIONS_WORKFLOW',
                className: 'ConnectionsWorkflow',
                name: 'connections-workflow',
              },
            },
            durable_objects: {
              bindings: [{ name: 'Connections', class_name: 'Connections' }],
              migrations: [
                {
                  tag: 'v1',
                  new_sqlite_classes: ['Connections'],
                },
              ],
            },
            r2_buckets: [
              {
                binding: 'SCREENSHOTS_INBOUND_BUCKET',
                bucket_name: 'test-screenshots-bucket',
              },
            ],
          },
          wrangler: { configPath: './wrangler.jsonc' },
        },
      },
    },
  };
});
